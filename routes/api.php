<?php

use App\Http\Controllers\ApiController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CalculateTreatmentController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\CancerController;
use App\Http\Controllers\CancerSummaryController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DurableController;
use App\Http\Controllers\ReferController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\Export_Types_of_cancer;
use App\Http\Controllers\ImportController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ExportController;
use App\Http\Controllers\NHSOController;
use App\Http\Controllers\OTP\OTPController;
use App\Http\Controllers\NotifyLogUserController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\WaitingTimeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::controller(AuthController::class)->group(function () {

    Route::post('login', 'login');
    Route::post('auth/login', 'authLogin');
    Route::post('auth/update', 'authUpdateInfomation');
    Route::post('auth/change-password', 'authChangePassword');
    Route::post('register', 'register');
    Route::post('logout', 'logout');
    Route::post('refresh', 'refresh');
    Route::post('login_mobile_app', 'login_mobile_app');
    // Route::post('login_mobile', 'login_mobile_app');
});

// Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
//     return $request->user();
// });

Route::get('/patientAllReference', [PatientController::class, 'patientAllReference']);

Route::get('/patientReferenceDistricts', [PatientController::class, 'patientReferenceDistricts']);
Route::get('/patientReferenceProvince', [PatientController::class, 'patientReferenceProvince']);
Route::get('/patientReferenceSubDistricts', [PatientController::class, 'patientReferenceSubDistricts']);
Route::get('/patientReferenceHospital', [PatientController::class, 'patientHospitals']);

Route::get('/mor', [PatientController::class, 'mor']);
//notify
Route::post('/alertNotify', [NotifyLogUserController::class, 'alertNotify']);
Route::get('/readNotify/{id}', [NotifyLogUserController::class, 'readNotify']);
Route::get('/getNotify', [NotifyLogUserController::class, 'get']);
Route::post('/getNotifyTable', [NotifyLogUserController::class, 'Page']);

Route::get('/patientIcd10', [PatientController::class, 'patientIcd10']);

Route::post('/diag', [CancerController::class, 'determineDiagnosisDateRequest']);
Route::middleware('auth:api')->group(function () {
    
    Route::post('/import/patient-dead', [ImportController::class, 'PatientDeadImport']);
    Route::get('/import/data', [ImportController::class, 'getImportData']);
    Route::post('/import/delete/{id}', [ImportController::class, 'deleteImportData']);
    // Route::get('/patientAllReference',[PatientController::class, 'patientAllReference']);
    Route::get('/patient/notification', [PatientController::class, 'patientNotification']);
    Route::get('/patientReference', [PatientController::class, 'patientReference']);
    Route::get('/patientPatientImportOtherHospital', [PatientController::class, 'patientPatientImportOtherHospital']);
    Route::get('/patientPatientById', [PatientController::class, 'patientPatientById']);
    Route::get('/patientPatientByCid', [PatientController::class, 'patientPatientByCid']);
    Route::get('/patientHospitalsList', [PatientController::class, 'patientHospitalsList']);
    Route::get('/patientPatientSearch', [PatientController::class, 'patientPatientSearch']);
    Route::post('/patientPatientTable', [PatientController::class, 'patientPatientTable']);
    Route::post('/patients', [PatientController::class, 'store']);
    Route::post('/patients-update/{id}', [PatientController::class, 'update']);
    Route::post('/patients-delete/{id}', [PatientController::class, 'delete']);
    Route::get('/patients/{id}/edit', [PatientController::class, 'edit']);
    Route::post('/cancer/import', [ImportController::class, 'importCancer']);
    // Route::post('/import/patient-dead', [ImportController::class, 'PatientDeadImport']);
    Route::get('/patientPatientSearchbyname', [PatientController::class, 'patientPatientSearchbyName']);

    Route::get('/getList', [DashboardController::class, 'getList']);
    Route::get('/getAcl', [DashboardController::class, 'getAcl']);
    Route::get('/getStat', [DashboardController::class, 'getStat']);
    Route::get('/getListAddress', [DashboardController::class, 'getListAddress']);
    Route::get('/getStatArea', [DashboardController::class, 'getStatArea']);
    Route::get('/getStatTop10', [DashboardController::class, 'getStatTop10']);
    Route::get('/getStatDay', [DashboardController::class, 'getStatDay']);
    Route::get('/getStatTop', [DashboardController::class, 'getStatTop']);

    Route::get('/refer/getReferImport', [ReferController::class, 'getReferImport']);
    Route::post('/refer/get-refer-receive-table', [ReferController::class, 'getReferReceiveTable']);
    Route::post('/refer/get-refer-send-table', [ReferController::class, 'getReferSendTable']);
    Route::get('/refer/getRefer/{id}', [ReferController::class, 'getRefer']);
    Route::get('/refer/getReferFullDetail', [ReferController::class, 'getReferFullDetail']);
    Route::get('/refer/patient', [ReferController::class, 'getReferPatient']);
    Route::delete('/refer/destroy/{id}', [ReferController::class, 'destroy']);
    Route::post('/refer/update/{id}', [ReferController::class, 'update']);
    Route::get('/refer/notification', [ReferController::class, 'getNotification']);
    Route::get('/refer/show/{id}', [ReferController::class, 'show']);
    Route::post('/refer/store', [ReferController::class, 'store']);
    Route::post('/refer/updateState', [ReferController::class, 'updateState']);
    Route::get('/refer/getNotifyReferPreAppoint', [ReferController::class, 'getNotifyReferPreAppoint']);
    Route::post('/refer/quickly', [ReferController::class, 'quickly']);
    // 
    Route::post('refer/upload-file', [ReferController::class, 'uploadFile']);

    Route::get('/report/hosin', [ReferController::class, 'hosin']);

    Route::get('/refer/getReportRefer', [ReferController::class, 'getReportRefer']);

    Route::get('/message', [ChatController::class, 'fetchMessages']);
    Route::post('/message', [ChatController::class, 'sendMessage']);

    Route::post('/durable/store', [DurableController::class, 'storeOrUpdate']);
    Route::get('/durable/show/{id}', [DurableController::class, 'show']);
    Route::post('/durable/update/{id}', [DurableController::class, 'update']);
    Route::delete('/durable/destroy/{id}', [DurableController::class, 'destroy']);
    Route::get('/durable/getReport', [DurableController::class, 'getReport']);
    Route::get('/durable/getValDurable', [DurableController::class, 'getValDurable']);
    Route::get('/durable/getDurable', [DurableController::class, 'getDurable']);
    Route::get('/durable/equipment', [DurableController::class, 'equipment']);

    Route::get('/cancers/get-icd10-wait-approve', [CancerController::class, 'getIcd10WaitApprove']);
    Route::get('/cancers/get-patient-cancer-approve', [CancerController::class, 'getPatientCancerApprove']);
    Route::get('/cancers/get-patient-cancer-history', [CancerController::class, 'patientCancerHistory']);
    Route::post('/cancers/get-first-approve-table', [CancerController::class, 'getFirstApproveTable']);
    Route::post('/cancers/get-exist-approve-table', [CancerController::class, 'getExistApproveTable']);
    Route::get('/cancers/get-cancer-by-patient-id', [CancerController::class, 'getCancerByPatientId']);
    Route::get('/cancers/{id}', [CancerController::class, 'show']);
    Route::post('/cancers', [CancerController::class, 'store']);
    Route::post('/cancers/{id}', [CancerController::class, 'update']);
    Route::post('/cancers-delete/{id}', [CancerController::class, 'destroy']);
    Route::get('/getcancerStage', [CancerController::class, 'getcancerStage']);
    Route::get('/getCancerTNM', [CancerController::class, 'getCancerTNM']);

    Route::post('/cancersums', [CancerSummaryController::class, 'store']);
    Route::get('/List/icd10', [CancerSummaryController::class, 'getlist_ICD10']);
    Route::get('/cancer/descripttion', [CancerSummaryController::class, 'get_description_ByID']);
    Route::get('/cancer/templete', [CancerSummaryController::class, 'get_Templete']);

    Route::post('/staff/Store_Or_Update', [StaffController::class, 'Store_Or_Update']);
    Route::get('/staff/show/{id}', [StaffController::class, 'show']);
    Route::patch('/staff/update/{id}', [StaffController::class, 'update']);
    Route::delete('/staff/destroy/{id}', [StaffController::class, 'destroy']);
    Route::get('/staff/getReport', [StaffController::class, 'getReport']);
    Route::get('/staff/Cancer_personnel_information', [StaffController::class, 'Cancer_personnel_information']);

    Route::get('/staff/getStaff', [StaffController::class, 'getStaff']);
    Route::get('/staff/getHospitalList', [StaffController::class, 'getHospitalList']);

    //ประเภทของโรคมะเร็ง
    Route::get('/excel/Export_number_type_treatment_Excel', [Export_Types_of_cancer::class, 'number_type_treatment_Excel']);
    
    Route::get('/excel/Export_Number_patients_by_address_Excel', [Export_Types_of_cancer::class, 'Number_patients_by_address_Excel']);
    Route::get('/app/partient', [Export_Types_of_cancer::class, 'partient']);
    Route::get('/app/hospital', [Export_Types_of_cancer::class, 'app_hospital']);
    Route::post('/store/suggest', [Export_Types_of_cancer::class, 'store_suggest']);
    Route::post('/store/template_suggest', [Export_Types_of_cancer::class, 'store_template_suggest']);
    Route::get('/report/Dashboard_group_cancer', [Export_Types_of_cancer::class, 'Dashboard_group_cancer']);
    
    Route::get('/report/waiting-time', [ReportController::class, 'waitingTime']);
    
    Route::get('/report/treatment_data', [ExportController::class, 'treatment_data']);
    Route::get('/report/Forward_data', [ReportController::class, 'Forward_data']);
    Route::get('/report/send_data', [ReportController::class, 'send_data']);
    Route::get('/report/Refuse', [ReportController::class, 'Refuse']);
   
    Route::get('/excel/rp01', [ReportController::class, 'rp01Excel']);
    Route::get('/excel/all', [ReportController::class, 'allExcel']);
    Route::get('/excel/ForwardExcel', [ReportController::class, 'ForwardExcel']);
    Route::get('/excel/RefuseExcel', [ReportController::class, 'RefuseExcel']);
    Route::get('/report/Browse_data', [ExportController::class, 'Browse_data']);
    Route::get('/report/total_dashboard', [ReportController::class, 'Total_dashboard']);
    Route::get('/report/Waiting', [ReportController::class, 'Waiting']);
    Route::get('/report/service_recipients', [ReportController::class, 'Split_code']);
    Route::get('/refer/detail', [ReferController::class, 'get_detail_quickly']);

    Route::get('/excel/Export_Types_of_cancer_Excel', [Export_Types_of_cancer::class, 'Export_Types_of_cancer_Excel']);
    Route::get('/excel/export_diagnosis', [ExportController::class, 'exportToExcel']);
    Route::get('/excel/export_extension', [ExportController::class, 'ExtensionTOExcel']);
    Route::get('/excel/export_stage', [ReportController::class, 'stageTOExcel']);
    Route::get('/excel/export/Patient_Details', [ExportController::class, 'Export_Cancer_Patient_Details']);
    Route::get('/excel/browse-data', [ExportController::class, 'Browse_dataTOExcel']);

    Route::get('/user/{id}', [UserController::class, 'getUserById']);
    Route::post('/user/create', [UserController::class, 'createUser']);
    Route::post('/user-update/{id}', [UserController::class, 'updateUser']);
    Route::delete('/user/{id}', [UserController::class, 'deleteUser']);
    Route::post('/user/datatable', [UserController::class, 'userDataTable']);
    Route::post('/user/force-change-password/{id}', [UserController::class, 'forceChangePassword']);

    Route::get('/excel/browse-visit-data', [ExportController::class, 'browseVisitData']);

    Route::post('/waiting-rp1', [WaitingTimeController::class, 'waitingRp1']);
    Route::post('/waiting-rp2', [WaitingTimeController::class, 'waitingRp2']);

    Route::get('/log/detail', [ReportController::class, 'getLogDetail']);
    Route::get('/log/user', [ReportController::class, 'getLogUser']);
    Route::get('/log/all', [ReportController::class, 'getLogAll']);

    Route::get('/report/rp01-detail', [ReportController::class, 'rp01ExcelDetail']);

    Route::get('/report-pop/rp01-detail', [ReportController::class, 'rp01ExcelDetailPop']);
});

Route::get('/log/api', [ApiController::class, 'getApiLog']);

Route::post('/user/reset-password', [UserController::class, 'resetPassword']);

Route::get('/app/partient', [Export_Types_of_cancer::class, 'partient']);
Route::get('/app/hospital', [Export_Types_of_cancer::class, 'app_hospital']);
Route::get('/app/cancer', [CancerController::class, 'app_cancer']);

Route::post('/Request_OTP', [OTPController::class, 'Request_OTP']);
Route::post('/Verify_OTP', [OTPController::class, 'Verify_OTP']);

Route::post('/wait/cancer', [WaitingTimeController::class, 'cancer']);
// Route::post('/treatment-group', [WaitingTimeController::class, 'treatmentGroup']);
Route::post('/treatment-group-test', [WaitingTimeController::class, 'waitting']);
Route::post('/report/popbase', [WaitingTimeController::class, 'waitingRp3']);

Route::post('/new', [WaitingTimeController::class, 'newWaitingTime']);
Route::post('/wait', [WaitingTimeController::class, 'wait']);
Route::post('/wait2', [WaitingTimeController::class, 'waitting2']);

Route::post('/treatment-group-prepair', [WaitingTimeController::class, 'createTreamentFromOldData']);

Route::get('/cancer/advice_mobile', [CancerSummaryController::class, 'advice_mobile']);
Route::get('/cancer/advice_mobileBYID', [CancerSummaryController::class, 'advice_mobileBYID']);
Route::get('/cancer/Appointment_list', [CancerSummaryController::class, 'Appointment_list']);

Route::post('/request-otp', [OTPController::class, 'RequestOTPUser']);
// Route::get('/report/Waiting', [ReportController::class, 'Waiting']);
Route::post('/verify-otp', [UserController::class, 'verifyOtp']);

Route::post('/ocr-morpho', [CancerController::class, 'ocrSave']);
Route::get('/ocr-morpho', [CancerController::class, 'ocrGet']);

Route::get('/caw/patients-cancer', [NHSOController::class, 'index']);
Route::get('/caw/patients-refer', [NHSOController::class, 'refer']);

Route::get('/report/rp01', [ReportController::class, 'rp01']);
Route::get('/report/diagnosis', [ExportController::class, 'diagnosis_data']);
Route::get('/report/Extension_data', [ExportController::class, 'Extension_data']);
Route::get('/report/Export_Types_of_cancer', [Export_Types_of_cancer::class, 'Export_Types_of_cancer']);
Route::get('/report/report-stage', [ReportController::class, 'reportStage']);
Route::get('/report/number_type_treatment', [Export_Types_of_cancer::class, 'number_type_treatment2']);
Route::get('/report/Number_patients_by_address', [Export_Types_of_cancer::class, 'Number_patients_by_address']);
Route::get('/report/pathology', [ReportController::class, 'reportPathology']);
Route::get('/report/muskeyin', [ReportController::class, 'getAllMuskeyinAPI']);

//Pop Base
Route::get('/report/pop-base', [WaitingTimeController::class, 'popbase466']);
Route::get('/report-pop/rp01', [ReportController::class, 'rp01Pop']);
Route::get('/report-pop/total_dashboard', [ReportController::class, 'Total_dashboardPop']);
Route::get('/report-pop/Dashboard_group_cancer', [Export_Types_of_cancer::class, 'Dashboard_group_cancer']);
Route::get('/report-pop/diagnosis', [ExportController::class, 'diagnosis_dataPop']);
Route::get('/report-pop/Extension_data', [ExportController::class, 'Extension_dataPop']);
Route::get('/report-pop/Export_Types_of_cancer', [Export_Types_of_cancer::class, 'Export_Types_of_cancerPop']);
Route::get('/report-pop/report-stage', [ReportController::class, 'reportStagePop']);
Route::get('/report-pop/number_type_treatment', [Export_Types_of_cancer::class, 'number_type_treatment2Pop']);
Route::get('/report-pop/Number_patients_by_address', [Export_Types_of_cancer::class, 'Number_patients_by_addressPop']);
Route::get('/report-pop/pathology', [ReportController::class, 'reportPathologyPop']);

Route::get('/report-pop/new-patient', [ReportController::class, 'newPatientPop']);
Route::get('/report-pop/pmv', [ReportController::class, 'summaryPMV']);
Route::get('/report-pop/dco', [ReportController::class, 'summaryDCO']);


Route::post('/hospital-data', [ExportController::class, 'hospitalData']);

// Route::get('/testtest', [CalculateTreatmentController::class, 'test']);
// Route::get('/testtest1/{cid}', [WaitingTimeController::class, 'waitCid']);
