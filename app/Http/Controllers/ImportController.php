<?php

namespace App\Http\Controllers;

use App\Imports\CancerDataImport;
use App\Imports\PatientDeadImport;
use App\Jobs\ProcessImportDaed;
use App\Models\DataPatient;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Jobs\ProcessImportDeadPatient;
use Illuminate\Support\Facades\Storage;

class ImportController extends Controller
{
    public function importCancer(Request $request)
    {
        // $user = Auth::user();

        //อ่านข้อมูลจากไฟล์
        $rows = Excel::toArray(new CancerDataImport, $request->file('file'));

        //เตรียมข้อมูลสำหรับตรวจสอบ
        $data = [];
        foreach ($rows[0] as $row) {
            $val = [
                'hospital_code' => $row[0],
                'hn_no' => $row[1],
                'title_code' => $row[2],
                'name' => $row[3],
                'last_name' => $row[4],
                'cid' => $row[5],
                'birth_date' => $row[6],
                'sex_code' => $row[7],
                'nationality_code' => $row[8],
                'death_date' => $row[9],
                'deathcause_code' => $row[10],

                'address_no' => $row[11],
                'address_moo' => $row[12],
                'area_code' => $row[13],
                'address_zipcode' => $row[14],

                'permanent_address_no' => $row[15],
                'permanent_address_moo' => $row[16],
                'permanent_area_code' => $row[17],
                'permanent_address_zipcode' => $row[18],

                'telephone_1' => $row[19],
                'telephone_2' => $row[20],

                'first_entrance_date' => $row[21],
                'entrance_date' => $row[22],
                'finance_support_code' => $row[23],
                'diagnosis_date' => $row[24],
                'diagnosis_code' => $row[25],
                'diagnosis_out' => $row[26],
                'excision_in_cut_date' => $row[27],
                'excision_in_read_date' => $row[28],
                'topo_code' => $row[29],
                'recurrent' => $row[30],
                'recurrent_date' => $row[31],
                'morphology_code' => $row[32],
                'behaviour_code' => $row[33],
                'grade_code' => $row[34],
                't_code' => $row[35],
                'n_code' => $row[36],
                'm_code' => $row[37],
                'tnm_date' => $row[38],
                'stage_code' => $row[39],
                'extension_code' => $row[40],
                'icd10_code' => $row[41],
                'met_1' => $row[42],
                'met_1_date' => $row[43],
                'met_2' => $row[44],
                'met_2_date' => $row[45],
                'met_3' => $row[46],
                'met_3_date' => $row[47],
                'met_4' => $row[48],
                'met_4_date' => $row[49],
                'met_5' => $row[50],
                'met_5_date' => $row[51],
                'met_6' => $row[52],
                'met_6_date' => $row[53],
                'met_7' => $row[54],
                'met_7_date' => $row[55],
                'met_7_other' => $row[56],
                'clinical_summary' => $row[57],
            ];

            $tr = [];
            for ($i = 58; $i < 117; $i += 6) {
                $tr[] = [
                    'treatment_code' => $row[$i],
                    'treatment_date' => $row[$i + 1],
                    'treatment_date_end' => $row[$i + 2],
                    'note' => $row[$i + 3],
                    'none_protocol' => $row[$i + 4],
                    'none_protocol_note' => $row[$i + 5],
                ];
            }

            $tr = array_filter($tr, function ($value) {
                return $value['treatment_code'] != null;
            });

            $val['treatments'] = $tr;
            $data[] = $val;
        }

        // ตรวจสอบ
        $validator = Validator::make($data, [
            '*.hospital_code' => 'required|exists:bd_hospital,code',
            '*.hn_no' => 'required',
            '*.title_code' => 'required|exists:bd_title,code',
            '*.name' => 'required',
            '*.last_name' => 'required',
            '*.cid' => 'required|regex:/^\d{13}$/',
            '*.birth_date' => 'required|date_format:d/m/Y',
            '*.sex_code' => 'required|exists:bd_sex,code',
            '*.nationality_code' => 'required|exists:bd_national,code',
            '*.death_date' => 'date_format:d/m/Y|nullable',
            '*.deathcause_code' => 'exists:bd_deathcause,code|nullable',

            '*.address_no' => 'required',
            '*.address_moo' => 'required',
            '*.area_code' => 'required|exists:bd_area,code',
            '*.address_zipcode' => 'required',

            '*.permanent_address_no' => 'required',
            '*.permanent_address_moo' => 'required',
            '*.permanent_area_code' => 'required|exists:bd_area,code',
            '*.permanent_address_zipcode' => 'required',

            '*.first_entrance_date' => 'required|date_format:d/m/Y',
            '*.entrance_date' => 'required|date_format:d/m/Y',
            '*.finance_support_code' => 'required|exists:bd_finance_support,code',
            '*.diagnosis_date' => 'required|date_format:d/m/Y',
            '*.diagnosis_code' => 'required|exists:bd_diagnosis,code',
            '*.diagnosis_out' => 'required|in:0,1',
            '*.excision_in_cut_date' => 'date_format:d/m/Y|nullable',
            '*.excision_in_read_date' => 'date_format:d/m/Y|nullable',
            '*.topo_code' => 'required|exists:bd_topo,code',
            '*.recurrent' => 'boolean|nullable',
            '*.recurrent_date' => 'date_format:d/m/Y|nullable',
            '*.morphology_code' => 'required|exists:bd_mor,code',
            '*.behaviour_code' => 'required|exists:bd_behaviour,code',
            '*.grade_code' => 'required|exists:bd_grade,code',
            '*.t_code' => 'exists:bd_t,code|nullable',
            '*.n_code' => 'exists:bd_n,code|nullable',
            '*.m_code' => 'exists:bd_m,code|nullable',
            '*.tnm_date' => 'date_format:d/m/Y|nullable',
            '*.stage_code' => 'exists:bd_tnm_stage,stage|nullable',
            '*.extension_code' => 'exists:bd_extend,code|nullable',
            '*.icd10_code' => 'required|exists:bd_icd10,ICD10',
            '*.met_1_date' => 'date_format:d/m/Y|nullable',
            '*.met_2_date' => 'date_format:d/m/Y|nullable',
            '*.met_3_date' => 'date_format:d/m/Y|nullable',
            '*.met_4_date' => 'date_format:d/m/Y|nullable',
            '*.met_5_date' => 'date_format:d/m/Y|nullable',
            '*.met_6_date' => 'date_format:d/m/Y|nullable',
            '*.met_7_date' => 'date_format:d/m/Y|nullable',
            '*.treatments.*.treatment_code' => 'required|exists:bd_treatment,code',
            '*.treatments.*.treatment_date' => 'required|date_format:d/m/Y',
            '*.treatments.*.treatment_date_end' => 'date_format:d/m/Y|nullable',
            '*.treatments.*.none_protocol' => 'boolean|nullable',
            '*.treatments.*.none_protocol_note' => 'max:255|nullable',
            '*.treatments.*.note' => 'max:255|nullable',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 400);
        }

        return response()->json([
            'status' => true,
            'message' => 'นำเข้าข้อมูลสำเร็จ',
        ]);

        // $this->insert2Database($user);
    }

    public function insert2Database($user)
    {
        $dataRow = DB::table('import_data')->get();

        foreach ($dataRow as $row) {
            $patient_id = null;

            // ค้นหาผู้ป่วย
            $data_patient = DB::table('data_patient')
                ->where('hospital_code', $row->hospital_code)
                ->where('hn_no', $row->hn_no)
                ->where('name', $row->name)
                ->where('last_name', $row->last_name)
                ->where('cid', $row->cid)
                ->where('sex_code', $row->sex_code)
                ->first();

            if ($data_patient == null) {
                $val_patient = $this->getPatientValue($row, $user);
                $patient_id = DB::table('data_patient')->insertGetId($val_patient);
            } else {
                $patient_id = $data_patient->id;
            }

            $cancer_id = null;

            $val_cancer = $this->getCancerValue($row, $patient_id, $user);
            $cancer_id = DB::table('data_cancer')->insertGetId($val_cancer);

            // บันทึกข้อมูลการรักษา (ถ้ามี)
            if (isset($row->treatment_code)) {
                $val_treatment = $this->getTreatment($row, $patient_id, $cancer_id);

                DB::table('data_treatment')->insertGetId($val_treatment);
            }

            DB::table('import_data')->where('id', $row->id)->update(['success' => 'y']);
        }
    }

    private function getPatientValue($row, $user)
    {
        $input = (array) $row;

        return [
            'hospital_code'                     => $user->hosCode,
            'hn_no'                             => $input['hn_no'] ?? null,
            'title_code'                        => $input['title_code'] ?? null,
            'name'                              => $input['name'] ?? null,
            'last_name'                         => $input['last_name'] ?? null,
            'cid'                               => $input['cid'] ?? null,
            'birth_date'                        => $input['birth_date'] ?? null,
            'sex_code'                          => $input['sex_code'] ?? null,
            'nationality_code'                  => $input['nationality_code'] ?? null,
            'death_date'                        => $input['death_date'] ?? null,
            'deathcause_code'                   => $input['deathcause_code'] ?? null,
            'address_no'                        => $input['address_no'] ?? null,
            'address_moo'                       => $input['address_moo'] ?? null,
            'address_province_id'               => $input['address_province_id'] ?? null,
            'address_district_id'               => $input['address_district_id'] ?? null,
            'address_sub_district_id'           => $input['address_sub_district_id'] ?? null,
            'area_code'                         => $input['address_sub_district_id'] ?? null,
            'permanent_address_no'              => $input['permanent_address_no'] ?? null,
            'permanent_address_moo'             => $input['permanent_address_moo'] ?? null,
            'permanent_address_province_id'     => $input['permanent_address_province_id'] ?? null,
            'permanent_address_district_id'     => $input['permanent_address_district_id'] ?? null,
            'permanent_address_sub_district_id' => $input['permanent_address_sub_district_id'] ?? null,
            'permanent_area_code'               => $input['permanent_address_sub_district_id'] ?? null,
            'telephone_1'                       => $input['telephone_1'] ?? null,
            'telephone_2'                       => $input['telephone_2'] ?? null,
            'updated_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            // 'updated_by'                        => $user->id,
            'created_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            // 'created_by'                        => $user->id
        ];
    }

    private function getCancerValue($data, $patient_id, $user)
    {
        $input = (array) $data;

        $topo_code  = CancerController::getCodeFromText($input, 'topo_text', 'bd_topo');
        $topo_id    = $topo_code ? substr($topo_code, 0, 2) : null;

        if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
            $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
            if ($mor) {
                $mor_key = $mor->key;
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                ], 422);
            }
        } else {
            $mor_key = null;
        }

        $val = [
            'hospital_code'             => $user->hosCode,
            'patient_id'                => $patient_id,
            'entrance_date'             => $input['entrance_date'] ?? null,
            'first_entrance_date'       => $input['first_entrance_date'] ?? null,
            'finance_support_code'      => CancerController::getCodeFromText($input, 'finance_support_text', 'bd_finance_support'),        // isset($input['finance_support_text']) && trim($input['finance_support_text']) != '' ? substr($input['finance_support_text'], 0, strpos($input['finance_support_text'], ' ')) : null,
            'finance_support_text'      => isset($input['finance_support_text']) ? $input['finance_support_text'] : null,
            'diagnosis_date'            => $input['diagnosis_date'],
            'diagnosis_code'            => isset($input['diagnosis_text']) ? substr($input['diagnosis_text'], 0, 1) : null,
            'diagnosis_text'            => isset($input['diagnosis_text']) ? $input['diagnosis_text'] : null,
            'diagnosis_out'             => isset($input['diagnosis_out']) ? $input['diagnosis_out'] : 0,
            'excision_in_cut_date'      => $input['excision_in_cut_date'] ?? null,
            'excision_in_read_date'     => $input['excision_in_read_date'] ?? null,
            'topo_id'                   => $topo_id,
            'topo_code'                 => $topo_code,
            'topo_text'                 => isset($input['topo_text']) ? $input['topo_text'] : null,
            'recurrent'                 => isset($input['recurrent']) ? $input['recurrent'] : null,
            'recurrent_date'            => $input['recurrent_date   '] ?? null,
            'morphology_code'           => $mor_key,
            'morphology_text'           => isset($input['morphology_text']) ? $input['morphology_text'] : null,
            'behaviour_code'            => CancerController::getCodeFromText($input, 'behaviour_text', 'bd_behaviour'),
            'behaviour_text'            => isset($input['behaviour_text']) ? $input['behaviour_text'] : null,
            'grade_code'                => CancerController::getCodeFromText($input, 'grade_text', 'bd_grade'),  // isset($input['grade_text']) ? substr($input['grade_text'], 0, strpos($input['grade_text'], ' ')) : null,
            'grade_text'                => isset($input['grade_text']) ? $input['grade_text'] : null,
            'm_code'                    => isset($input['m_code']) ? intval($input['m_code']) : null,
            'n_code'                    => isset($input['n_code']) ? intval($input['n_code']) : null,
            't_code'                    => isset($input['t_code']) ? intval($input['t_code']) : null,
            'tnm_date'                  => $input['tnm_date'] ?? null,
            'stage_code'                => isset($input['stage_code']) ? intval($input['stage_code']) : null,
            'extension_code'            => isset($input['extension_code']) ? intval($input['extension_code']) : null,
            'icd10_code'                => isset($input['icd10_code']) ? $input['icd10_code'] : null,
            'icd10_text'                => isset($input['icd10_text']) ? $input['icd10_text'] : null,
            'met_1'                     => isset($input['met_1']) ? $input['met_1'] : 0,
            'met_1_date'                => $input['met_1_date'] ?? null,
            'met_2'                     => isset($input['met_2']) ? $input['met_2'] : 0,
            'met_2_date'                => $input['met_2_date'] ?? null,
            'met_3'                     => isset($input['met_3']) ? $input['met_3'] : 0,
            'met_3_date'                => $input['met_3_date'] ?? null,
            'met_4'                     => isset($input['met_4']) ? $input['met_4'] : 0,
            'met_4_date'                => $input['met_4_date'] ?? null,
            'met_5'                     => isset($input['met_5']) ? $input['met_5'] : 0,
            'met_5_date'                => $input['met_5_date'] ?? null,
            'met_6'                     => isset($input['met_6']) ? $input['met_6'] : 0,
            'met_6_date'                => $input['met_6_date'] ?? null,
            'met_7'                     => isset($input['met_7']) ? $input['met_7'] : 0,
            'met_7_date'                => $input['met_7_date'] ?? null,
            'met_7_other'               => $input['met_7_other'] ?? null,
            'clinical_summary'          => isset($input['clinical_summary']) ? $input['clinical_summary'] : null,
            'created_by'                => $user->id,
            'created_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'user_cancer'               => 0,
            'source_id'                 => 6,
        ];

        return $val;
    }
    private function getTreatment($data, $patient_id, $cancer_id)
    {
        $input = (array) $data;

        return [
            'patient_id'            => $patient_id,
            'cancer_id'             => $cancer_id,
            'treatment_type_id'     => CancerController::getCodeFromText($input, 'treatment_code', 'bd_treatment'),
            'treatment_code'        => $input['treatment_code'] ?? null,
            'treatment_date'        => $input['treatment_date'] ?? null,
            'treatment_date_end'    => $input['treatment_date_end'] ?? null,
            'consult_date'          => $input['consult_date'] ?? null,
            'note'                  => $input['note'] ?? null,
            'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'            => Carbon::now()->format('Y-m-d H:i:s')
        ];
    }

    // public function importPatientDead(Request $request)
    // {
    //     $batch = Bus::batch([])->dispatch();

    //     for ($i = 0; $i < 100; $i++) {
    //         $batch->add(new ProcessImportDaed());
    //     }

    //     return response()->json([
    //         'status' => true,
    //         'message' => 'Batch job dispatched successfully.',
    //         'batch_id' => $batch->id,
    //     ]);
    // }

    public function PatientDeadImport(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first(),], 400);
        }

        $file = $request->file('file');

        $fileSave = $this->saveFile($file);

        $import_id = DB::table('import_table')->insertGetId([
            'detail'        => 'นำเข้าผู้เสียชีวิต',
            'path'          => $fileSave['file_path'],
            'created_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'    => $user->id,
            'updated_by'    => $user->id,
        ]);

        ProcessImportDeadPatient::dispatch($import_id, $fileSave['file_path']);

        return response()->json([
            'status' => true,
            'message' => 'นำเข้าข้อมูลสำเร็จ',
        ]);
    }

    public function getImportData()
    {
        $user = Auth::user();

        $import = DB::table('import_table')->get();

        return response()->json([
            'status' => true,
            'data' => $import,
        ]);
    }


    function columnLetterFromIndex($index)
    {
        $letter = '';
        while ($index > 0) {
            $index--;
            $letter = chr(65 + ($index % 26)) . $letter;
            $index = intval($index / 26);
        }
        return $letter;
    }

    public function saveFile($file)
    {
        $filename = Carbon::now()->timestamp . '-' . $file->getClientOriginalName();

        $path = 'imports/' . Carbon::now()->format('Ymd');

        $file->storeAs($path, $filename, 'public');

        $filepath = $path . '/' . $filename;

        $fileUrl = env('APP_URL') . '/upload/' . $filepath;

        return [
            'file_path' => $filepath,
            'file_url' => $fileUrl,
        ];
    }

    public function deleteImportData($id)
    {
        $data = DB::table('import_table')->where('id', $id)->first();

        if (!$data) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูล',
            ], 404);
        }

        //delete file
        Storage::disk('public')->delete($data->path);

        DB::table('import_table')->where('id', $id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'ลบข้อมูลสำเร็จ',
        ]);
    }
}
