<?php

namespace App\Http\Controllers;

use App\Imports\CancerDataImport;
use App\Imports\PatientDeadImport;
use App\Jobs\ProcessImportDaed;
use App\Jobs\ProcessImportCancer;
use App\Models\DataPatient;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Validator;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Jobs\ProcessImportDeadPatient;
use Illuminate\Support\Facades\Storage;

class ImportController extends Controller
{
    public function importCancer(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first(),], 400);
        }

        $file = $request->file('file');

        $fileSave = $this->saveFile($file);

        $import_id = DB::table('import_table')->insertGetId([
            'detail'        => 'นำเข้าข้อมูลโรคมะเร็ง',
            'path'          => $fileSave['file_path'],
            'created_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'    => $user->id,
            'updated_by'    => $user->id,
        ]);

        ProcessImportCancer::dispatch($import_id, $fileSave['file_path'], $user->id);

        return response()->json([
            'status' => true,
            'message' => 'เริ่มการนำเข้าข้อมูลแล้ว กรุณารอสักครู่',
            'import_id' => $import_id
        ]);
    }

    public function insert2Database($user)
    {
        $dataRow = DB::table('import_data')->get();

        foreach ($dataRow as $row) {
            $patient_id = null;

            // ค้นหาผู้ป่วย
            $data_patient = DB::table('data_patient')
                ->where('hospital_code', $row->hospital_code)
                ->where('hn_no', $row->hn_no)
                ->where('name', $row->name)
                ->where('last_name', $row->last_name)
                ->where('cid', $row->cid)
                ->where('sex_code', $row->sex_code)
                ->first();

            if ($data_patient == null) {
                $val_patient = $this->getPatientValue($row, $user);
                $patient_id = DB::table('data_patient')->insertGetId($val_patient);
            } else {
                $patient_id = $data_patient->id;
            }

            $cancer_id = null;

            $val_cancer = $this->getCancerValue($row, $patient_id, $user);
            $cancer_id = DB::table('data_cancer')->insertGetId($val_cancer);

            // บันทึกข้อมูลการรักษา (ถ้ามี)
            if (isset($row->treatment_code)) {
                $val_treatment = $this->getTreatment($row, $patient_id, $cancer_id);

                DB::table('data_treatment')->insertGetId($val_treatment);
            }

            DB::table('import_data')->where('id', $row->id)->update(['success' => 'y']);
        }
    }

    private function getPatientValue($row, $user)
    {
        $input = (array) $row;

        return [
            'hospital_code'                     => $user->hosCode,
            'hn_no'                             => $input['hn_no'] ?? null,
            'title_code'                        => $input['title_code'] ?? null,
            'name'                              => $input['name'] ?? null,
            'last_name'                         => $input['last_name'] ?? null,
            'cid'                               => $input['cid'] ?? null,
            'birth_date'                        => $input['birth_date'] ?? null,
            'sex_code'                          => $input['sex_code'] ?? null,
            'nationality_code'                  => $input['nationality_code'] ?? null,
            'death_date'                        => $input['death_date'] ?? null,
            'deathcause_code'                   => $input['deathcause_code'] ?? null,
            'address_no'                        => $input['address_no'] ?? null,
            'address_moo'                       => $input['address_moo'] ?? null,
            'address_province_id'               => $input['address_province_id'] ?? null,
            'address_district_id'               => $input['address_district_id'] ?? null,
            'address_sub_district_id'           => $input['address_sub_district_id'] ?? null,
            'area_code'                         => $input['address_sub_district_id'] ?? null,
            'permanent_address_no'              => $input['permanent_address_no'] ?? null,
            'permanent_address_moo'             => $input['permanent_address_moo'] ?? null,
            'permanent_address_province_id'     => $input['permanent_address_province_id'] ?? null,
            'permanent_address_district_id'     => $input['permanent_address_district_id'] ?? null,
            'permanent_address_sub_district_id' => $input['permanent_address_sub_district_id'] ?? null,
            'permanent_area_code'               => $input['permanent_address_sub_district_id'] ?? null,
            'telephone_1'                       => $input['telephone_1'] ?? null,
            'telephone_2'                       => $input['telephone_2'] ?? null,
            'updated_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            // 'updated_by'                        => $user->id,
            'created_at'                        => Carbon::now()->format('Y-m-d H:i:s'),
            // 'created_by'                        => $user->id
        ];
    }

    private function getCancerValue($data, $patient_id, $user)
    {
        $input = (array) $data;

        $topo_code  = CancerController::getCodeFromText($input, 'topo_text', 'bd_topo');
        $topo_id    = $topo_code ? substr($topo_code, 0, 2) : null;

        if (isset($input['morphology_text']) && $input['morphology_text'] != '') {
            $mor = DB::table('bd_mor')->where(DB::raw("concat(code, ' ', name)"), $input['morphology_text'])->first();
            if ($mor) {
                $mor_key = $mor->key;
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'Morphology ไม่ถูกต้อง กรุณาเลือกจากตัวเลือกเท่านั้น'
                ], 422);
            }
        } else {
            $mor_key = null;
        }

        $val = [
            'hospital_code'             => $user->hosCode,
            'patient_id'                => $patient_id,
            'entrance_date'             => $input['entrance_date'] ?? null,
            'first_entrance_date'       => $input['first_entrance_date'] ?? null,
            'finance_support_code'      => CancerController::getCodeFromText($input, 'finance_support_text', 'bd_finance_support'),        // isset($input['finance_support_text']) && trim($input['finance_support_text']) != '' ? substr($input['finance_support_text'], 0, strpos($input['finance_support_text'], ' ')) : null,
            'finance_support_text'      => isset($input['finance_support_text']) ? $input['finance_support_text'] : null,
            'diagnosis_date'            => $input['diagnosis_date'],
            'diagnosis_code'            => isset($input['diagnosis_text']) ? substr($input['diagnosis_text'], 0, 1) : null,
            'diagnosis_text'            => isset($input['diagnosis_text']) ? $input['diagnosis_text'] : null,
            'diagnosis_out'             => isset($input['diagnosis_out']) ? $input['diagnosis_out'] : 0,
            'excision_in_cut_date'      => $input['excision_in_cut_date'] ?? null,
            'excision_in_read_date'     => $input['excision_in_read_date'] ?? null,
            'topo_id'                   => $topo_id,
            'topo_code'                 => $topo_code,
            'topo_text'                 => isset($input['topo_text']) ? $input['topo_text'] : null,
            'recurrent'                 => isset($input['recurrent']) ? $input['recurrent'] : null,
            'recurrent_date'            => $input['recurrent_date   '] ?? null,
            'morphology_code'           => $mor_key,
            'morphology_text'           => isset($input['morphology_text']) ? $input['morphology_text'] : null,
            'behaviour_code'            => CancerController::getCodeFromText($input, 'behaviour_text', 'bd_behaviour'),
            'behaviour_text'            => isset($input['behaviour_text']) ? $input['behaviour_text'] : null,
            'grade_code'                => CancerController::getCodeFromText($input, 'grade_text', 'bd_grade'),  // isset($input['grade_text']) ? substr($input['grade_text'], 0, strpos($input['grade_text'], ' ')) : null,
            'grade_text'                => isset($input['grade_text']) ? $input['grade_text'] : null,
            'm_code'                    => isset($input['m_code']) ? intval($input['m_code']) : null,
            'n_code'                    => isset($input['n_code']) ? intval($input['n_code']) : null,
            't_code'                    => isset($input['t_code']) ? intval($input['t_code']) : null,
            'tnm_date'                  => $input['tnm_date'] ?? null,
            'stage_code'                => isset($input['stage_code']) ? intval($input['stage_code']) : null,
            'extension_code'            => isset($input['extension_code']) ? intval($input['extension_code']) : null,
            'icd10_code'                => isset($input['icd10_code']) ? $input['icd10_code'] : null,
            'icd10_text'                => isset($input['icd10_text']) ? $input['icd10_text'] : null,
            'met_1'                     => isset($input['met_1']) ? $input['met_1'] : 0,
            'met_1_date'                => $input['met_1_date'] ?? null,
            'met_2'                     => isset($input['met_2']) ? $input['met_2'] : 0,
            'met_2_date'                => $input['met_2_date'] ?? null,
            'met_3'                     => isset($input['met_3']) ? $input['met_3'] : 0,
            'met_3_date'                => $input['met_3_date'] ?? null,
            'met_4'                     => isset($input['met_4']) ? $input['met_4'] : 0,
            'met_4_date'                => $input['met_4_date'] ?? null,
            'met_5'                     => isset($input['met_5']) ? $input['met_5'] : 0,
            'met_5_date'                => $input['met_5_date'] ?? null,
            'met_6'                     => isset($input['met_6']) ? $input['met_6'] : 0,
            'met_6_date'                => $input['met_6_date'] ?? null,
            'met_7'                     => isset($input['met_7']) ? $input['met_7'] : 0,
            'met_7_date'                => $input['met_7_date'] ?? null,
            'met_7_other'               => $input['met_7_other'] ?? null,
            'clinical_summary'          => isset($input['clinical_summary']) ? $input['clinical_summary'] : null,
            'created_by'                => $user->id,
            'created_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'                => Carbon::now()->format('Y-m-d H:i:s'),
            'user_cancer'               => 0,
            'source_id'                 => 6,
        ];

        return $val;
    }
    private function getTreatment($data, $patient_id, $cancer_id)
    {
        $input = (array) $data;

        return [
            'patient_id'            => $patient_id,
            'cancer_id'             => $cancer_id,
            'treatment_type_id'     => CancerController::getCodeFromText($input, 'treatment_code', 'bd_treatment'),
            'treatment_code'        => $input['treatment_code'] ?? null,
            'treatment_date'        => $input['treatment_date'] ?? null,
            'treatment_date_end'    => $input['treatment_date_end'] ?? null,
            'consult_date'          => $input['consult_date'] ?? null,
            'note'                  => $input['note'] ?? null,
            'created_at'            => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'            => Carbon::now()->format('Y-m-d H:i:s')
        ];
    }

    // public function importPatientDead(Request $request)
    // {
    //     $batch = Bus::batch([])->dispatch();

    //     for ($i = 0; $i < 100; $i++) {
    //         $batch->add(new ProcessImportDaed());
    //     }

    //     return response()->json([
    //         'status' => true,
    //         'message' => 'Batch job dispatched successfully.',
    //         'batch_id' => $batch->id,
    //     ]);
    // }

    public function PatientDeadImport(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:xlsx,xls',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first(),], 400);
        }

        $file = $request->file('file');

        $fileSave = $this->saveFile($file);

        $import_id = DB::table('import_table')->insertGetId([
            'detail'        => 'นำเข้าผู้เสียชีวิต',
            'path'          => $fileSave['file_path'],
            'created_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'updated_at'    => Carbon::now()->format('Y-m-d H:i:s'),
            'created_by'    => $user->id,
            'updated_by'    => $user->id,
        ]);

        ProcessImportDeadPatient::dispatch($import_id, $fileSave['file_path']);

        return response()->json([
            'status' => true,
            'message' => 'นำเข้าข้อมูลสำเร็จ',
        ]);
    }

    public function getImportData()
    {
        $user = Auth::user();

        $import = DB::table('import_table')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'status' => true,
            'data' => $import,
        ]);
    }

    public function getImportStatus($id)
    {
        $import = DB::table('import_table')->where('id', $id)->first();

        if (!$import) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูลการ import',
            ], 404);
        }

        return response()->json([
            'status' => true,
            'data' => $import,
        ]);
    }

    public function downloadErrorFile($id)
    {
        $import = DB::table('import_table')->where('id', $id)->first();

        if (!$import || !$import->error_file) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบไฟล์ error',
            ], 404);
        }

        $filePath = storage_path('app/public/' . $import->error_file);

        if (!file_exists($filePath)) {
            return response()->json([
                'status' => false,
                'message' => 'ไฟล์ไม่พบในระบบ',
            ], 404);
        }

        return response()->download($filePath);
    }


    function columnLetterFromIndex($index)
    {
        $letter = '';
        while ($index > 0) {
            $index--;
            $letter = chr(65 + ($index % 26)) . $letter;
            $index = intval($index / 26);
        }
        return $letter;
    }

    public function saveFile($file)
    {
        $filename = Carbon::now()->timestamp . '-' . $file->getClientOriginalName();

        $path = 'imports/' . Carbon::now()->format('Ymd');

        $file->storeAs($path, $filename, 'public');

        $filepath = $path . '/' . $filename;

        $fileUrl = env('APP_URL') . '/upload/' . $filepath;

        return [
            'file_path' => $filepath,
            'file_url' => $fileUrl,
        ];
    }

    public function deleteImportData($id)
    {
        $data = DB::table('import_table')->where('id', $id)->first();

        if (!$data) {
            return response()->json([
                'status' => false,
                'message' => 'ไม่พบข้อมูล',
            ], 404);
        }

        //delete file
        Storage::disk('public')->delete($data->path);

        DB::table('import_table')->where('id', $id)->delete();

        return response()->json([
            'status' => true,
            'message' => 'ลบข้อมูลสำเร็จ',
        ]);
    }
}
