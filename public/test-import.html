<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบ Import ข้อมูลโรคมะเร็ง</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }
        .upload-section.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        input[type="file"] {
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #28a745;
            width: 0%;
            transition: width 0.3s ease;
        }
        .result-item {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .result-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .result-error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .result-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .download-link {
            display: inline-block;
            margin-top: 10px;
            padding: 8px 16px;
            background-color: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
        .download-link:hover {
            background-color: #c82333;
        }
        .import-history {
            margin-top: 30px;
        }
        .history-item {
            padding: 15px;
            margin: 10px 0;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .history-item h4 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .history-stats {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .stat-item {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .stat-total { background-color: #e9ecef; }
        .stat-success { background-color: #d4edda; color: #155724; }
        .stat-error { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 ระบบ Import ข้อมูลโรคมะเร็ง</h1>
        
        <div class="upload-section" id="uploadSection">
            <h3>📁 อัพโหลดไฟล์ Excel</h3>
            <p>ลากไฟล์มาวางที่นี่ หรือคลิกเพื่อเลือกไฟล์</p>
            <input type="file" id="fileInput" accept=".xlsx,.xls" />
            <br>
            <button id="uploadBtn" onclick="uploadFile()">🚀 เริ่มการ Import</button>
        </div>

        <div class="status-section" id="statusSection" style="display: none;">
            <h3>📊 สถานะการประมวลผล</h3>
            <div id="statusContent"></div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">กำลังเตรียมข้อมูล...</div>
        </div>

        <div class="import-history">
            <h3>📋 ประวัติการ Import</h3>
            <button onclick="loadImportHistory()">🔄 รีเฟรช</button>
            <div id="historyContent">กำลังโหลด...</div>
        </div>
    </div>

    <script>
        const API_BASE = '/api';
        const TOKEN = 'your-api-token-here'; // ใส่ token ที่ได้จากการ login

        let currentImportId = null;
        let statusCheckInterval = null;

        // Drag and drop functionality
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');

        uploadSection.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadSection.classList.add('dragover');
        });

        uploadSection.addEventListener('dragleave', () => {
            uploadSection.classList.remove('dragover');
        });

        uploadSection.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadSection.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
            }
        });

        uploadSection.addEventListener('click', () => {
            fileInput.click();
        });

        async function uploadFile() {
            const file = fileInput.files[0];
            if (!file) {
                alert('กรุณาเลือกไฟล์');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('statusSection').style.display = 'block';
            
            try {
                const response = await fetch(`${API_BASE}/import/cancer`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    },
                    body: formData
                });

                const data = await response.json();
                
                if (data.status) {
                    currentImportId = data.import_id;
                    showStatus('เริ่มการ Import แล้ว กำลังประมวลผล...', 'info');
                    startStatusCheck();
                } else {
                    showStatus(`เกิดข้อผิดพลาด: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus(`เกิดข้อผิดพลาด: ${error.message}`, 'error');
            } finally {
                document.getElementById('uploadBtn').disabled = false;
            }
        }

        function startStatusCheck() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(async () => {
                if (!currentImportId) return;

                try {
                    const response = await fetch(`${API_BASE}/import/status/${currentImportId}`, {
                        headers: {
                            'Authorization': `Bearer ${TOKEN}`
                        }
                    });

                    const result = await response.json();
                    
                    if (result.status && result.data) {
                        const data = result.data;
                        
                        if (data.finished_at) {
                            // การ import เสร็จสิ้น
                            clearInterval(statusCheckInterval);
                            showFinalResult(data);
                            loadImportHistory();
                        } else {
                            // ยังไม่เสร็จ
                            updateProgress(data);
                        }
                    }
                } catch (error) {
                    console.error('Error checking status:', error);
                }
            }, 2000); // ตรวจสอบทุก 2 วินาที
        }

        function updateProgress(data) {
            const total = data.total || 0;
            const processed = (data.ok || 0) + (data.fail || 0);
            const percentage = total > 0 ? (processed / total) * 100 : 0;

            document.getElementById('progressFill').style.width = `${percentage}%`;
            document.getElementById('progressText').textContent = 
                `กำลังประมวลผล... (${processed}/${total} แถว)`;
        }

        function showFinalResult(data) {
            const total = data.total || 0;
            const success = data.ok || 0;
            const failed = data.fail || 0;

            let resultHtml = `
                <div class="result-item result-success">
                    ✅ การ Import เสร็จสิ้น!<br>
                    📊 สรุปผล: ทั้งหมด ${total} แถว | สำเร็จ ${success} แถว | ล้มเหลว ${failed} แถว
                </div>
            `;

            if (data.error_file) {
                resultHtml += `
                    <div class="result-item result-error">
                        ⚠️ พบข้อมูลที่ไม่ผ่านการตรวจสอบ ${failed} แถว
                        <a href="${API_BASE}/import/download-error/${data.id}" class="download-link" target="_blank">
                            📥 ดาวน์โหลดไฟล์ Error Report
                        </a>
                    </div>
                `;
            }

            document.getElementById('statusContent').innerHTML = resultHtml;
            document.getElementById('progressFill').style.width = '100%';
            document.getElementById('progressText').textContent = 'เสร็จสิ้น!';
        }

        function showStatus(message, type) {
            const className = `result-${type}`;
            document.getElementById('statusContent').innerHTML = 
                `<div class="result-item ${className}">${message}</div>`;
        }

        async function loadImportHistory() {
            try {
                const response = await fetch(`${API_BASE}/import/data`, {
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    }
                });

                const result = await response.json();
                
                if (result.status && result.data) {
                    displayImportHistory(result.data);
                } else {
                    document.getElementById('historyContent').innerHTML = 
                        '<div class="result-item result-error">ไม่สามารถโหลดประวัติได้</div>';
                }
            } catch (error) {
                document.getElementById('historyContent').innerHTML = 
                    '<div class="result-item result-error">เกิดข้อผิดพลาดในการโหลดประวัติ</div>';
            }
        }

        function displayImportHistory(imports) {
            if (imports.length === 0) {
                document.getElementById('historyContent').innerHTML = 
                    '<div class="result-item result-info">ยังไม่มีประวัติการ Import</div>';
                return;
            }

            let html = '';
            imports.forEach(item => {
                const status = item.finished_at ? 'เสร็จสิ้น' : 'กำลังประมวลผล';
                const statusClass = item.finished_at ? 'success' : 'info';
                
                html += `
                    <div class="history-item">
                        <h4>${item.detail}</h4>
                        <div class="history-stats">
                            <span class="stat-item stat-total">ทั้งหมด: ${item.total || 0}</span>
                            <span class="stat-item stat-success">สำเร็จ: ${item.ok || 0}</span>
                            <span class="stat-item stat-error">ล้มเหลว: ${item.fail || 0}</span>
                        </div>
                        <div>
                            <strong>สถานะ:</strong> <span class="result-${statusClass}">${status}</span><br>
                            <strong>เริ่มเมื่อ:</strong> ${new Date(item.created_at).toLocaleString('th-TH')}<br>
                            ${item.finished_at ? `<strong>เสร็จเมื่อ:</strong> ${new Date(item.finished_at).toLocaleString('th-TH')}<br>` : ''}
                        </div>
                        ${item.error_file ? `
                            <a href="${API_BASE}/import/download-error/${item.id}" class="download-link" target="_blank">
                                📥 ดาวน์โหลด Error Report
                            </a>
                        ` : ''}
                    </div>
                `;
            });

            document.getElementById('historyContent').innerHTML = html;
        }

        // โหลดประวัติเมื่อเริ่มต้น
        loadImportHistory();
    </script>
</body>
</html>
